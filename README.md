# AutoGen Chat Application

一个基于AutoGen 0.5.7的智能聊天应用，具有炫酷的前端界面和流式输出功能。

## 🌟 特性

- **智能对话**: 基于AutoGen 0.5.7实现的智能助手
- **流式输出**: 使用SSE协议实现实时流式响应
- **炫酷界面**: 参考Gemini风格的现代化UI设计
- **响应式设计**: 支持桌面端和移动端
- **玻璃态效果**: 现代化的毛玻璃视觉效果
- **动画交互**: 丰富的动画和过渡效果

## 🏗️ 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **AutoGen 0.5.7**: 微软开源的多代理对话框架
- **SSE**: Server-Sent Events流式输出
- **Uvicorn**: ASGI服务器

### 前端
- **React 18**: 现代化的前端框架
- **TypeScript**: 类型安全的JavaScript
- **Vite**: 快速的构建工具
- **Ant Design**: 企业级UI组件库
- **Ant Design X**: 流式组件支持
- **Framer Motion**: 动画库
- **React Markdown**: Markdown渲染

## 📁 项目结构

```
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI主应用
│   ├── autogen_service.py  # AutoGen服务封装
│   ├── requirements.txt    # Python依赖
│   └── .env               # 环境变量配置
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── types/         # TypeScript类型定义
│   │   └── ...
│   ├── package.json       # Node.js依赖
│   └── vite.config.ts     # Vite配置
├── example/               # 原有的AutoGen示例代码
├── start_backend.py       # 后端启动脚本
├── start_frontend.py      # 前端启动脚本
└── README.md             # 项目说明
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 1. 配置环境变量

编辑 `backend/.env` 文件，配置您的AI模型API：

```env
MODEL=deepseek-chat
BASE_URL=https://api.deepseek.com/v1
API_KEY=your_api_key_here
```

### 2. 启动后端服务

```bash
python start_backend.py
```

后端服务将在 http://localhost:8000 启动

### 3. 启动前端服务

```bash
python start_frontend.py
```

前端服务将在 http://localhost:3000 启动

### 4. 开始使用

打开浏览器访问 http://localhost:3000，开始与AI助手对话！

## 📖 使用说明

### 主要功能

1. **智能对话**: 支持多轮对话，AI会记住上下文
2. **流式输出**: 实时显示AI回复，提供流畅的用户体验
3. **Markdown支持**: 支持代码块、列表、引用等格式
4. **代码高亮**: 自动识别并高亮显示代码
5. **响应式设计**: 完美适配桌面和移动设备

### 操作指南

- **发送消息**: 在输入框输入文字，按Enter或点击发送按钮
- **换行**: Shift+Enter可以在输入框中换行
- **清除对话**: 点击清除按钮可以清空当前对话历史
- **示例问题**: 点击欢迎页面的示例问题快速开始

## 🛠️ 开发说明

### 后端开发

主要文件：
- `backend/main.py`: FastAPI应用主文件
- `backend/autogen_service.py`: AutoGen服务封装
- `backend/.env`: 环境变量配置

### 前端开发

主要目录：
- `frontend/src/components/`: React组件
- `frontend/src/hooks/`: 自定义Hooks
- `frontend/src/types/`: TypeScript类型定义

### 自定义配置

1. **修改AI模型**: 编辑 `backend/.env` 文件
2. **调整界面主题**: 修改 `frontend/src/index.css` 中的CSS变量
3. **自定义AI行为**: 编辑 `backend/autogen_service.py` 中的system_message

## 🔧 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本是否为3.8+
   - 确保已安装所有依赖：`pip install -r backend/requirements.txt`
   - 检查API_KEY是否正确配置

2. **前端启动失败**
   - 检查Node.js版本是否为16+
   - 删除node_modules文件夹，重新运行 `npm install`
   - 尝试使用yarn代替npm

3. **无法连接到后端**
   - 确保后端服务正在运行
   - 检查防火墙设置
   - 确认端口8000未被占用

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🙏 致谢

- [AutoGen](https://github.com/microsoft/autogen) - 微软开源的多代理框架
- [Ant Design](https://ant.design/) - 企业级UI设计语言
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
