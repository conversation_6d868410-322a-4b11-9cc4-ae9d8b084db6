#!/usr/bin/env python3
"""
后端服务启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    # 确保在正确的目录中
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    
    if not backend_dir.exists():
        print("❌ backend目录不存在！")
        return
    
    os.chdir(backend_dir)
    
    print("🚀 启动AutoGen Chat后端服务")
    print(f"📁 工作目录: {backend_dir}")
    print(f"📡 服务地址: http://localhost:8000")
    print(f"📚 API文档: http://localhost:8000/docs")
    print("-" * 50)
    
    # 检查是否安装了依赖
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        print("📦 检查Python依赖...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, cwd=backend_dir)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("⚠️  依赖安装失败，请手动安装")
    
    # 启动服务
    try:
        subprocess.run([sys.executable, "-m", "uvicorn", "main:app", 
                       "--host", "0.0.0.0", "--port", "8000", "--reload"],
                      cwd=backend_dir)
    except KeyboardInterrupt:
        print("\n👋 后端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
