from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import asyncio
import json
import logging
from typing import AsyncGenerator
from autogen_service import AutoGenService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="AutoGen Chat API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AutoGen服务
autogen_service = AutoGenService()

class ChatMessage(BaseModel):
    message: str
    conversation_id: str = "default"

@app.get("/")
async def root():
    """根路径"""
    return {"message": "AutoGen Chat API is running!"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "autogen-chat"}

async def generate_chat_response(message: str, conversation_id: str) -> AsyncGenerator[str, None]:
    """生成聊天响应的异步生成器"""
    try:
        logger.info(f"开始处理消息: {message}")
        
        # 使用AutoGen服务生成流式响应
        async for chunk in autogen_service.chat_stream(message, conversation_id):
            # 构造SSE格式的数据
            response_data = {
                "content": chunk,
                "is_complete": False
            }
            yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
            
            # 添加小延迟以确保流式效果
            await asyncio.sleep(0.01)
        
        # 发送完成信号
        final_data = {
            "content": "",
            "is_complete": True
        }
        yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
        
    except Exception as e:
        logger.error(f"生成响应时出错: {str(e)}")
        error_data = {
            "content": f"抱歉，处理您的请求时出现了错误: {str(e)}",
            "is_complete": True,
            "error": True
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

@app.post("/chat/stream")
async def chat_stream(chat_message: ChatMessage):
    """流式聊天端点"""
    try:
        return StreamingResponse(
            generate_chat_response(chat_message.message, chat_message.conversation_id),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
    except Exception as e:
        logger.error(f"聊天流式处理错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chat/{conversation_id}/history")
async def get_conversation_history(conversation_id: str):
    """获取对话历史"""
    try:
        history = autogen_service.get_conversation_history(conversation_id)
        return {"conversation_id": conversation_id, "history": history}
    except Exception as e:
        logger.error(f"获取对话历史错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/chat/{conversation_id}")
async def clear_conversation(conversation_id: str):
    """清除对话历史"""
    try:
        autogen_service.clear_conversation(conversation_id)
        return {"message": f"对话 {conversation_id} 已清除"}
    except Exception as e:
        logger.error(f"清除对话错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理工作"""
    await autogen_service.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
