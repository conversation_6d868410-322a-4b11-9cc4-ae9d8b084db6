#!/usr/bin/env python3
"""
前端服务启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    # 确保在正确的目录中
    project_root = Path(__file__).parent.absolute()
    frontend_dir = project_root / "frontend"
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在！")
        return
    
    os.chdir(frontend_dir)
    
    print("🚀 启动AutoGen Chat前端服务")
    print(f"📁 工作目录: {frontend_dir}")
    print(f"🌐 服务地址: http://localhost:3000")
    print("-" * 50)
    
    # 检查是否安装了Node.js依赖
    package_json = frontend_dir / "package.json"
    node_modules = frontend_dir / "node_modules"
    
    if package_json.exists() and not node_modules.exists():
        print("📦 安装Node.js依赖...")
        try:
            subprocess.run(["npm", "install"], check=True, cwd=frontend_dir)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("⚠️  npm安装失败，尝试使用yarn...")
            try:
                subprocess.run(["yarn", "install"], check=True, cwd=frontend_dir)
                print("✅ 依赖安装完成")
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败，请手动运行 npm install 或 yarn install")
                return
    
    # 启动开发服务器
    try:
        # 优先使用npm
        subprocess.run(["npm", "run", "dev"], cwd=frontend_dir)
    except FileNotFoundError:
        try:
            # 如果npm不可用，尝试yarn
            subprocess.run(["yarn", "dev"], cwd=frontend_dir)
        except FileNotFoundError:
            print("❌ 未找到npm或yarn，请确保已安装Node.js")
    except KeyboardInterrupt:
        print("\n👋 前端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
