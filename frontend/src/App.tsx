import React, { useState, useRef, useEffect } from 'react';
import { Layout, Input, Button, Card, Typography, Space, message, Tooltip } from 'antd';
import { SendOutlined, ClearOutlined, RobotOutlined, UserOutlined } from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import ChatMessage from './components/ChatMessage';
import StreamingText from './components/StreamingText';
import { useChatStream } from './hooks/useChatStream';
import { Message } from './types/chat';
import './App.css';

const { Header, Content, Footer } = Layout;
const { TextArea } = Input;
const { Title, Text } = Typography;

const App: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);

  const { sendMessage, clearConversation } = useChatStream();

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isStreaming) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsStreaming(true);

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isStreaming: true,
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      await sendMessage(inputValue, (chunk: string) => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === assistantMessage.id 
              ? { ...msg, content: msg.content + chunk }
              : msg
          )
        );
      });
    } catch (error) {
      message.error('发送消息失败，请重试');
      console.error('Send message error:', error);
    } finally {
      setIsStreaming(false);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === assistantMessage.id 
            ? { ...msg, isStreaming: false }
            : msg
        )
      );
    }
  };

  // 清除对话
  const handleClearConversation = async () => {
    try {
      await clearConversation();
      setMessages([]);
      message.success('对话已清除');
    } catch (error) {
      message.error('清除对话失败');
      console.error('Clear conversation error:', error);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 示例问题
  const exampleQuestions = [
    "你好，请介绍一下你自己",
    "帮我写一个Python快速排序算法",
    "解释一下什么是机器学习",
    "推荐几本好看的科幻小说"
  ];

  return (
    <Layout className="app-layout">
      <Header className="app-header glass">
        <motion.div 
          className="header-content"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Space align="center">
            <RobotOutlined className="header-icon" />
            <Title level={3} className="header-title">
              AutoGen Chat
            </Title>
          </Space>
          <Text className="header-subtitle">
            基于 AutoGen 0.5.7 的智能对话助手
          </Text>
        </motion.div>
      </Header>

      <Content className="app-content">
        <div className="chat-container">
          <div className="messages-container">
            <AnimatePresence>
              {messages.length === 0 ? (
                <motion.div 
                  className="welcome-section"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="welcome-card glass">
                    <div className="welcome-content">
                      <RobotOutlined className="welcome-icon" />
                      <Title level={2} className="welcome-title">
                        欢迎使用 AutoGen Chat
                      </Title>
                      <Text className="welcome-description">
                        我是您的智能助手，可以帮您解答问题、编写代码、创作内容等。
                        请在下方输入您的问题开始对话。
                      </Text>
                      
                      <div className="example-questions">
                        <Text strong>试试这些问题：</Text>
                        <div className="questions-grid">
                          {exampleQuestions.map((question, index) => (
                            <motion.div
                              key={index}
                              className="question-card"
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => setInputValue(question)}
                            >
                              {question}
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ) : (
                messages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <ChatMessage message={message} />
                  </motion.div>
                ))
              )}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </div>

          <div className="input-section glass">
            <div className="input-container">
              <TextArea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="输入您的问题... (Enter 发送，Shift+Enter 换行)"
                autoSize={{ minRows: 1, maxRows: 4 }}
                className="message-input"
                disabled={isStreaming}
              />
              <div className="input-actions">
                <Tooltip title="清除对话">
                  <Button
                    icon={<ClearOutlined />}
                    onClick={handleClearConversation}
                    disabled={isStreaming || messages.length === 0}
                    className="action-button"
                  />
                </Tooltip>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isStreaming}
                  loading={isStreaming}
                  className="send-button"
                >
                  发送
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Content>

      <Footer className="app-footer">
        <Text className="footer-text">
          Powered by AutoGen 0.5.7 & FastAPI | 
          <span className="footer-highlight"> 智能对话，无限可能</span>
        </Text>
      </Footer>
    </Layout>
  );
};

export default App;
