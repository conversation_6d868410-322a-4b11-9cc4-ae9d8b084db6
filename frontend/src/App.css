.app-layout {
  min-height: 100vh;
  background: transparent;
}

.app-header {
  padding: 0 24px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  text-align: center;
}

.header-icon {
  font-size: 32px;
  color: var(--accent-color);
  margin-right: 12px;
}

.header-title {
  color: var(--text-primary) !important;
  margin: 0 !important;
  font-weight: 700;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 备用颜色，防止渐变不支持 */
  color: var(--primary-color) !important;
}

.header-subtitle {
  color: var(--text-secondary);
  font-size: 14px;
  display: block;
  margin-top: 4px;
}

.app-content {
  flex: 1;
  padding: 0;
  background: transparent;
}

.chat-container {
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px;
  margin-bottom: 24px;
}

.welcome-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.welcome-card {
  max-width: 600px;
  width: 100%;
  border: none;
  border-radius: var(--radius-xl);
}

.welcome-content {
  text-align: center;
  padding: 40px 20px;
}

.welcome-icon {
  font-size: 64px;
  color: var(--accent-color);
  margin-bottom: 24px;
  display: block;
}

.welcome-title {
  color: var(--text-primary) !important;
  margin-bottom: 16px !important;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 备用颜色，防止渐变不支持 */
  color: var(--primary-color) !important;
}

.welcome-description {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
  display: block;
}

.example-questions {
  text-align: left;
}

.example-questions > span {
  color: var(--text-primary);
  font-size: 16px;
  margin-bottom: 16px;
  display: block;
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.question-card {
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.question-card:hover {
  background: var(--bg-glass);
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.input-section {
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 1px solid var(--border-color);
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-md) !important;
  color: var(--text-primary) !important;
  font-size: 16px;
  resize: none;
}

.message-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.message-input::placeholder {
  color: var(--text-muted) !important;
}

.input-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-button {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: var(--bg-glass);
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.send-button {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) !important;
  border: none !important;
  border-radius: var(--radius-sm) !important;
  height: 40px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: var(--shadow-light);
}

.send-button:hover {
  background: linear-gradient(45deg, var(--secondary-color), var(--accent-color)) !important;
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.send-button:disabled {
  background: var(--bg-dark) !important;
  color: var(--text-muted) !important;
  transform: none;
  box-shadow: none;
}

.app-footer {
  background: var(--bg-glass);
  border-top: 1px solid var(--border-color);
  text-align: center;
  padding: 16px 24px;
  backdrop-filter: blur(20px);
}

.footer-text {
  color: var(--text-secondary);
  font-size: 14px;
}

.footer-highlight {
  color: var(--accent-color);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    padding: 16px;
    height: calc(100vh - 120px);
  }
  
  .app-header {
    height: 60px;
    padding: 0 16px;
  }
  
  .header-icon {
    font-size: 24px;
  }
  
  .header-title {
    font-size: 20px !important;
  }
  
  .welcome-card {
    margin: 0 16px;
  }
  
  .welcome-content {
    padding: 24px 16px;
  }
  
  .welcome-icon {
    font-size: 48px;
  }
  
  .questions-grid {
    grid-template-columns: 1fr;
  }
  
  .input-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .input-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .send-button {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .chat-container {
    padding: 12px;
  }
  
  .input-section {
    padding: 16px;
  }
  
  .welcome-content {
    padding: 20px 12px;
  }
}
