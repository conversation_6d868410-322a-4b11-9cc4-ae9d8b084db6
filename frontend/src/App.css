.app-layout {
  min-height: 100vh;
  background: transparent;
}

.app-header {
  padding: 0;
  height: 120px;
  position: sticky;
  top: 0;
  z-index: 100;
  overflow: hidden;
  background: transparent;
  border: none;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.95) 0%,
    rgba(118, 75, 162, 0.95) 50%,
    rgba(240, 147, 251, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.header-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  animation: float 6s ease-in-out infinite;
}

.header-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(240, 147, 251, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: pulse-glow 4s ease-in-out infinite;
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  z-index: 2;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.header-icon {
  font-size: 28px;
  color: #ffffff;
  z-index: 2;
}

.icon-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgba(240, 147, 251, 0.6);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

.header-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-title {
  margin: 0 !important;
  font-weight: 800;
  font-size: 32px !important;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.title-gradient {
  background: linear-gradient(45deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: #ffffff;
}

.title-accent {
  color: #fbbf24;
  text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.subtitle-icon {
  animation: sparkle 2s ease-in-out infinite;
}

.subtitle-badge {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  border: 1px solid rgba(251, 191, 36, 0.3);
  margin-left: 8px;
}

.header-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse-dot 2s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(180deg);
  }
}

/* 头部悬停效果 */
.header-icon-container:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.header-icon-container:hover .header-icon {
  color: #fbbf24;
  transition: all 0.3s ease;
}

.status-indicator:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.app-content {
  flex: 1;
  padding: 0;
  background: transparent;
}

.chat-container {
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px;
  margin-bottom: 24px;
}

.welcome-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.welcome-card {
  max-width: 600px;
  width: 100%;
  border: none;
  border-radius: var(--radius-xl);
}

.welcome-content {
  text-align: center;
  padding: 40px 20px;
}

.welcome-icon {
  font-size: 64px;
  color: var(--accent-color);
  margin-bottom: 24px;
  display: block;
}

.welcome-title {
  color: var(--text-primary) !important;
  margin-bottom: 16px !important;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 备用颜色，防止渐变不支持 */
  color: var(--primary-color) !important;
}

.welcome-description {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
  display: block;
}

.example-questions {
  text-align: left;
}

.example-questions > span {
  color: var(--text-primary);
  font-size: 16px;
  margin-bottom: 16px;
  display: block;
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.question-card {
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.question-card:hover {
  background: var(--bg-glass);
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.input-section {
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 1px solid var(--border-color);
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: var(--bg-glass) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-md) !important;
  color: var(--text-primary) !important;
  font-size: 16px;
  resize: none;
}

.message-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.message-input::placeholder {
  color: var(--text-muted) !important;
}

.input-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-button {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: var(--bg-glass);
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.send-button {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) !important;
  border: none !important;
  border-radius: var(--radius-sm) !important;
  height: 40px;
  padding: 0 20px;
  font-weight: 600;
  box-shadow: var(--shadow-light);
}

.send-button:hover {
  background: linear-gradient(45deg, var(--secondary-color), var(--accent-color)) !important;
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.send-button:disabled {
  background: var(--bg-dark) !important;
  color: var(--text-muted) !important;
  transform: none;
  box-shadow: none;
}

.app-footer {
  background: var(--bg-glass);
  border-top: 1px solid var(--border-color);
  text-align: center;
  padding: 16px 24px;
  backdrop-filter: blur(20px);
}

.footer-text {
  color: var(--text-secondary);
  font-size: 14px;
}

.footer-highlight {
  color: var(--accent-color);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    padding: 16px;
    height: calc(100vh - 140px);
  }

  .app-header {
    height: 100px;
  }

  .header-content {
    padding: 0 16px;
    flex-direction: column;
    gap: 8px;
  }

  .header-main {
    gap: 12px;
  }

  .header-icon-container {
    width: 50px;
    height: 50px;
  }

  .header-icon {
    font-size: 22px;
  }

  .header-title {
    font-size: 24px !important;
  }

  .header-subtitle {
    font-size: 12px;
  }

  .subtitle-badge {
    display: none;
  }

  .header-status {
    position: absolute;
    top: 8px;
    right: 16px;
  }

  .status-indicator {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .welcome-card {
    margin: 0 16px;
  }
  
  .welcome-content {
    padding: 24px 16px;
  }
  
  .welcome-icon {
    font-size: 48px;
  }
  
  .questions-grid {
    grid-template-columns: 1fr;
  }
  
  .input-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .input-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .send-button {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .chat-container {
    padding: 12px;
    height: calc(100vh - 120px);
  }

  .app-header {
    height: 80px;
  }

  .header-content {
    padding: 0 12px;
  }

  .header-main {
    gap: 8px;
  }

  .header-icon-container {
    width: 40px;
    height: 40px;
  }

  .header-icon {
    font-size: 18px;
  }

  .header-title {
    font-size: 20px !important;
  }

  .header-subtitle {
    font-size: 11px;
  }

  .header-glow {
    width: 200px;
    height: 200px;
  }

  .input-section {
    padding: 16px;
  }

  .welcome-content {
    padding: 20px 12px;
  }
}
