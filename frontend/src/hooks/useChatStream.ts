import { useState, useCallback } from 'react';
import { ChatResponse } from '../types/chat';

const API_BASE_URL = 'http://localhost:8000';

export const useChatStream = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (
    message: string,
    onChunk: (chunk: string) => void,
    conversationId: string = 'default'
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversation_id: conversationId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: ChatResponse = JSON.parse(line.slice(6));
              
              if (data.error) {
                throw new Error(data.content);
              }
              
              if (data.content) {
                onChunk(data.content);
              }
              
              if (data.is_complete) {
                break;
              }
            } catch (parseError) {
              console.warn('解析SSE数据失败:', parseError);
            }
          }
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发送消息失败';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearConversation = useCallback(async (conversationId: string = 'default') => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat/${conversationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '清除对话失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const getConversationHistory = useCallback(async (conversationId: string = 'default') => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat/${conversationId}/history`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取对话历史失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    sendMessage,
    clearConversation,
    getConversationHistory,
    isLoading,
    error,
  };
};
