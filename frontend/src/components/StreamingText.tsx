import React from 'react';

interface StreamingTextProps {
  text: string;
  isComplete?: boolean;
}

const StreamingText: React.FC<StreamingTextProps> = ({ text, isComplete = false }) => {
  return (
    <div className="streaming-text">
      <span style={{ whiteSpace: 'pre-wrap' }}>{text}</span>
      {!isComplete && text && (
        <span className="streaming-cursor">|</span>
      )}
    </div>
  );
};

export default StreamingText;
