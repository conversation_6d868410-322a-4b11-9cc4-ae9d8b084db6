import React, { useState, useEffect } from 'react';
import { But<PERSON>, Card } from 'antd';
import StreamingText from './StreamingText';

const TestStreaming: React.FC = () => {
  const [text, setText] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);

  const testText = "这是一个测试流式输出的示例。我们将逐字显示这段文字，以验证流式组件是否正常工作。每个字符都会依次出现，模拟真实的AI回复效果。";

  const startStreaming = () => {
    setText('');
    setIsStreaming(true);
    
    let index = 0;
    const interval = setInterval(() => {
      if (index < testText.length) {
        setText(testText.slice(0, index + 1));
        index++;
      } else {
        setIsStreaming(false);
        clearInterval(interval);
      }
    }, 50);
  };

  return (
    <Card title="流式输出测试" style={{ margin: '20px', maxWidth: '600px' }}>
      <div style={{ marginBottom: '16px' }}>
        <Button onClick={startStreaming} disabled={isStreaming}>
          {isStreaming ? '正在流式输出...' : '开始测试'}
        </Button>
      </div>
      
      <div style={{ 
        padding: '16px', 
        background: 'rgba(255,255,255,0.1)', 
        borderRadius: '8px',
        minHeight: '100px'
      }}>
        <StreamingText text={text} isComplete={!isStreaming} />
      </div>
    </Card>
  );
};

export default TestStreaming;
