import React from 'react';
import { Card, Avatar, Typography, Space } from 'antd';
import { UserOutlined, RobotOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Message } from '../types/chat';
import StreamingText from './StreamingText';
import './ChatMessage.css';

const { Text } = Typography;

interface ChatMessageProps {
  message: Message;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.role === 'user';
  const isStreaming = message.isStreaming;

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderContent = () => {
    if (isUser) {
      return (
        <div className="message-content user-content">
          {message.content}
        </div>
      );
    }

    if (isStreaming) {
      return (
        <div className="message-content assistant-content">
          <StreamingText text={message.content} isComplete={false} />
        </div>
      );
    }

    return (
      <div className="message-content assistant-content">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  style={vscDarkPlus}
                  language={match[1]}
                  PreTag="div"
                  className="code-block"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className="inline-code" {...props}>
                  {children}
                </code>
              );
            },
            p: ({ children }) => <p className="markdown-paragraph">{children}</p>,
            ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
            ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
            li: ({ children }) => <li className="markdown-list-item">{children}</li>,
            blockquote: ({ children }) => (
              <blockquote className="markdown-blockquote">{children}</blockquote>
            ),
            h1: ({ children }) => <h1 className="markdown-heading">{children}</h1>,
            h2: ({ children }) => <h2 className="markdown-heading">{children}</h2>,
            h3: ({ children }) => <h3 className="markdown-heading">{children}</h3>,
            h4: ({ children }) => <h4 className="markdown-heading">{children}</h4>,
            h5: ({ children }) => <h5 className="markdown-heading">{children}</h5>,
            h6: ({ children }) => <h6 className="markdown-heading">{children}</h6>,
          }}
        >
          {message.content}
        </ReactMarkdown>
      </div>
    );
  };

  return (
    <motion.div
      className={`message-wrapper ${isUser ? 'user-message' : 'assistant-message'}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="message-container">
        {!isUser && (
          <Avatar
            icon={<RobotOutlined />}
            className="message-avatar assistant-avatar"
            size={40}
          />
        )}
        
        <Card className={`message-card ${isUser ? 'user-card' : 'assistant-card'} glass`}>
          {renderContent()}
          <div className="message-meta">
            <Text className="message-time">
              {formatTime(message.timestamp)}
            </Text>
            {isStreaming && (
              <div className="streaming-indicator">
                <div className="dot"></div>
                <div className="dot"></div>
                <div className="dot"></div>
              </div>
            )}
          </div>
        </Card>

        {isUser && (
          <Avatar
            icon={<UserOutlined />}
            className="message-avatar user-avatar"
            size={40}
          />
        )}
      </div>
    </motion.div>
  );
};

export default ChatMessage;
