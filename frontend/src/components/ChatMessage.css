.message-wrapper {
  margin-bottom: 24px;
  width: 100%;
}

.message-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 100%;
}

.user-message .message-container {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.user-avatar {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.assistant-avatar {
  background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.message-card {
  flex: 1;
  max-width: 80%;
  border: none;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.user-card {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  margin-left: auto;
}

.assistant-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
}

.message-content {
  padding: 16px 20px 8px;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.user-content {
  color: var(--text-primary);
  font-size: 15px;
}

.assistant-content {
  color: var(--text-primary);
  font-size: 15px;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.message-time {
  font-size: 12px;
  color: var(--text-muted);
}

.streaming-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.streaming-indicator .dot {
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.streaming-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.streaming-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* 流式文本样式 */
.streaming-text {
  display: inline-block;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: 100%;
}

.streaming-cursor {
  color: var(--accent-color);
  font-weight: bold;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Markdown 样式 */
.markdown-paragraph {
  margin: 0 0 12px 0;
  line-height: 1.6;
}

.markdown-paragraph:last-child {
  margin-bottom: 0;
}

.markdown-heading {
  color: var(--text-primary);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.markdown-heading:first-child {
  margin-top: 0;
}

.markdown-list {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-list-item {
  margin: 4px 0;
  line-height: 1.5;
}

.markdown-blockquote {
  border-left: 4px solid var(--primary-color);
  padding: 12px 16px;
  margin: 16px 0;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
  font-style: italic;
}

.code-block {
  border-radius: var(--radius-sm) !important;
  margin: 12px 0 !important;
  font-size: 14px !important;
  box-shadow: var(--shadow-light);
}

.inline-code {
  background: rgba(102, 126, 234, 0.2);
  color: var(--accent-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-card {
    max-width: 90%;
  }
  
  .message-content {
    padding: 12px 16px 6px;
    font-size: 14px;
  }
  
  .message-meta {
    padding: 6px 16px 10px;
  }
  
  .message-avatar {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .message-card {
    max-width: 95%;
  }
  
  .message-container {
    gap: 8px;
  }
  
  .message-content {
    padding: 10px 12px 4px;
  }
  
  .message-meta {
    padding: 4px 12px 8px;
  }
}
